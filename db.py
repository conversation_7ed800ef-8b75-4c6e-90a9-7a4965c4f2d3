import psycopg2
import threading
import time
import os
import psutil

# -----------------------------------------------------------------------------
# STEP 1: D<PERSON><PERSON><PERSON><PERSON> CREDENTIALS AND CONFIGURATION
# -----------------------------------------------------------------------------
# You must fill in your PostgreSQL database details here
DB_NAME = "simulateload"
DB_USER = "postgres"
DB_PASS = "postgres"
DB_HOST = "localhost"
DB_PORT = "5432"

# Configuration for the test
TEST_TABLE_NAME = "cpu_memory_test_table"
DATA_SIZE = 1000000 
MONITOR_DURATION_SECONDS = 60 # Now a longer duration for verification

# Global flag to signal when to stop the monitoring thread
stop_monitoring_event = threading.Event()
# Global variable to hold the PID of the load-generating process
load_pid_event = threading.Event()
load_pid = None

# -----------------------------------------------------------------------------
# STEP 2: TABLE SETUP AND CLEANUP (UNCHANGED)
# -----------------------------------------------------------------------------
def setup_database():
    """Creates the test table and populates it with data."""
    try:
        conn = psycopg2.connect(dbname=DB_NAME, user=DB_USER, password=DB_PASS, host=DB_HOST, port=DB_PORT)
        cursor = conn.cursor()
        print("Creating and populating test table...")
        cursor.execute(f"DROP TABLE IF EXISTS {TEST_TABLE_NAME};")
        cursor.execute(f"CREATE TABLE {TEST_TABLE_NAME} (id SERIAL PRIMARY KEY, data1 INTEGER, data2 VARCHAR(255), random_data FLOAT);")
        conn.commit()
        print(f"Inserting {DATA_SIZE} rows...")
        insert_query = f"""
            INSERT INTO {TEST_TABLE_NAME} (data1, data2, random_data)
            SELECT generate_series(1, {DATA_SIZE}), md5(random()::text), random() * 1000;
        """
        cursor.execute(insert_query)
        conn.commit()
        print("Table setup complete.")
        return True
    except Exception as e:
        print(f"Error during setup: {e}")
        return False
    finally:
        if conn:
            conn.close()

def cleanup_database():
    """Drops the test table."""
    try:
        conn = psycopg2.connect(dbname=DB_NAME, user=DB_USER, password=DB_PASS, host=DB_HOST, port=DB_PORT)
        cursor = conn.cursor()
        print(f"Dropping table {TEST_TABLE_NAME}...")
        cursor.execute(f"DROP TABLE IF EXISTS {TEST_TABLE_NAME};")
        conn.commit()
        print("Cleanup complete.")
    except Exception as e:
        print(f"Error during cleanup: {e}")
    finally:
        if conn:
            conn.close()

# -----------------------------------------------------------------------------
# STEP 3: LOAD GENERATION THREAD (WITH SLEEP FUNCTION)
# -----------------------------------------------------------------------------
def generate_load_query():
    """Executes a single query that sleeps for 50 seconds."""
    global load_pid
    try:
        conn = psycopg2.connect(dbname=DB_NAME, user=DB_USER, password=DB_PASS, host=DB_HOST, port=DB_PORT)
        cursor = conn.cursor()
        
        # Get the PID of this connection
        cursor.execute("SELECT pg_backend_pid();")
        load_pid = cursor.fetchone()[0]
        load_pid_event.set() # Signal that the PID is available

        print(f"Load generation thread started. My PID is {load_pid}.")
        print("Executing a query that will sleep for 50 seconds...")

        # The query to hold the process
        load_query = f"SELECT pg_sleep(80);"

        # Execute the query
        cursor.execute(load_query)
        conn.commit()
        print("Load query finished successfully (this should only happen if not killed).")

    except psycopg2.OperationalError as e:
        # This exception is expected if the process is terminated by the kill command
        if "terminating connection due to administrator command" in str(e):
            print(f"\nSUCCESS: The load process with PID {load_pid} was terminated by an administrator command.")
        else:
            print(f"Error in load generator thread: {e}")
    except Exception as e:
        print(f"Error in load generator thread: {e}")
    finally:
        if conn:
            conn.close()
            print("Load generation thread finished.")

# -----------------------------------------------------------------------------
# STEP 4: MONITORING THREAD (UNCHANGED)
# -----------------------------------------------------------------------------
def monitor_load():
    """Monitors the load-generating process using its PID."""
    print("\nMonitoring thread started. Waiting for load PID...")
    load_pid_event.wait() # Wait until the load PID is available

    try:
        process = psutil.Process(load_pid)
    except psutil.NoSuchProcess:
        print(f"Error: Process with PID {load_pid} not found.")
        return

    print(f"\nMonitoring process {load_pid} for {MONITOR_DURATION_SECONDS} seconds...")

    for i in range(MONITOR_DURATION_SECONDS):
        try:
            # Check if the process is still running
            if not process.is_running():
                print(f"Process with PID {load_pid} is no longer running. Test complete.")
                break

            # Get CPU and memory usage from the operating system
            cpu_percent = process.cpu_percent(interval=1)
            mem_info = process.memory_info()
            mem_percent = process.memory_percent()

            # Now, get the state from the database
            conn = psycopg2.connect(dbname=DB_NAME, user=DB_USER, password=DB_PASS, host=DB_HOST, port=DB_PORT)
            cursor = conn.cursor()
            cursor.execute(f"SELECT state, query FROM pg_stat_activity WHERE pid = {load_pid};")
            db_state = cursor.fetchone()
            
            if db_state:
                state, query = db_state
                print(f"\n--- Monitoring Report (Time: {i+1}s) ---")
                print(f"Process ID (PID): {load_pid}")
                print(f"PostgreSQL State: {state}")
                print(f"CPU Utilization: {cpu_percent:.2f}%")
                print(f"Memory Usage: {mem_info.rss / (1024 * 1024):.2f} MB ({mem_percent:.2f}%)")
                print(f"Query: {query[:50]}...")
                print("---------------------------------------")
            else:
                print(f"Process {load_pid} no longer active in database.")
                break
        
        except Exception as e:
            print(f"Error during monitoring: {e}")
            break
        finally:
            if 'conn' in locals() and conn:
                conn.close()
    
    stop_monitoring_event.set()
    print("\nMonitoring thread finished.")

# -----------------------------------------------------------------------------
# MAIN EXECUTION FLOW (UNCHANGED)
# -----------------------------------------------------------------------------
if __name__ == "__main__":
    if setup_database():
        load_thread = threading.Thread(target=generate_load_query)
        monitor_thread = threading.Thread(target=monitor_load)
        
        load_thread.start()
        monitor_thread.start()

        monitor_thread.join()
        load_thread.join()
        
        cleanup_database()
    else:
        print("Script failed due to database setup error.")